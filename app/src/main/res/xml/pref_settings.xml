<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory app:title="APP Info">
        <Preference
            app:key="version"
            app:icon="@drawable/icon"
            app:title="Version"
            />
        <Preference
            app:key="me0"
            app:icon="@drawable/ultranity"
            app:title="Ultranity"
            android:summary="@string/ultranity_subtext"
            />
        <Preference
            app:key="me"
            app:icon="@drawable/xinobu"
            app:title="Perol_Notsf"
            android:summary="@string/notsf_subtext2"
            />
        <Preference
            app:key="me1"
            app:title="Right Now"
            app:icon="@drawable/rightnow"
            android:summary="@string/rightnow_subtext"
            />
        <Preference
            app:key="icons"
            app:icon="@drawable/nizhuren"
            app:title="@string/changeicons"
            app:summary="@string/icon_draw_by"
            />
        <Preference
            app:key="check"
            app:icon="@drawable/ic_sync"
            app:title="@string/checkupdate" />
    </PreferenceCategory>
    <PreferenceCategory app:title="Switch">
        <Preference
            app:key="APIConfig"
            app:title="@string/api_config"
            app:summary="@string/set_mirror"/>
        <Preference
            app:key="ProxyConfig"
            app:title="@string/proxy_config"
            app:summary="@string/proxy_help_text"/>
        <SwitchPreferenceCompat
            android:summaryOn="@string/h_is_ok"
            app:defaultValue="false"
            app:key="r18on"
            app:summaryOff="@string/h_not_ok"
            app:title="@string/_18" />
        <ListPreference
            app:key="CollectMode"
            android:entries="@array/CollectMode"
            android:entryValues="@array/CollectMode_value"
            app:defaultValue="0"
            app:title="@string/collectmode"
            app:useSimpleSummaryProvider="true" />
        <SwitchPreferenceCompat
            app:defaultValue="true"
            app:key="resume_unfinished_task"
            app:summaryOff="@string/resume_unfinished_task_off"
            app:summaryOn="@string/resume_unfinished_task_on"
            app:title="@string/unfinished_task" />
        <SwitchPreferenceCompat
            app:defaultValue="false"
            app:key="check_clipboard"
            app:summary="@string/check_clipboard_policy"
            app:title="@string/check_clipboard" />
        <SwitchPreferenceCompat
            app:key="ShowDownloadToast"
            app:defaultValue="true"
            app:title="@string/show_download_toast" />
        <SwitchPreferenceCompat
            app:key="AutoLoadRelatedIllust"
            app:defaultValue="true"
            app:title="@string/auto_load_related_illust" />
    </PreferenceCategory>
    <PreferenceCategory app:title="Selection">
        <Preference
            app:key="storepath1"
            app:title="@string/savepath" />
        <SwitchPreferenceCompat
            app:key="needcreatefold"
            app:defaultValue="false"
            app:title="@string/create_separate_folder"
           app:summary="@string/create_painter_folder"/>
        <SwitchPreferenceCompat
            app:key="R18Folder"
            app:defaultValue="false"
            app:title="@string/create_R18_folder"/>
        <SeekBarPreference
            app:key="restrictSanity"
            app:defaultValue="7"
            app:min="1"
            android:max="7"
            app:showSeekBarValue="true"
            app:title="@string/max_sanity"
            app:summary="@string/restrict_sanity" />
        <SwitchPreferenceCompat
            app:key="R18Private"
            app:defaultValue="true"
            app:title="@string/R18_private"/>
        <SwitchPreferenceCompat
            app:key="enableonlybookmarked"
            app:defaultValue="true"
            app:title="@string/enableonlybookmarked"/>
        <Preference
            app:key="filesaveformat"
            app:defaultValue="false"
            app:title="@string/filesaveformat"/>
        <ListPreference
            android:entries="@array/firstpage"
            android:entryValues="@array/firstpage_value"
            app:defaultValue="0"
            app:key="firstpage"
            app:title="@string/startpage"
            app:useSimpleSummaryProvider="true" />
        <ListPreference
            android:entryValues="@array/quality_value"
            app:defaultValue="0"
            app:entries="@array/quality"
            app:key="quality"
            app:title="@string/pic_quality"
            app:useSimpleSummaryProvider="true" />
        <ListPreference
            android:entryValues="@array/quality_download_value"
            app:defaultValue="2"
            app:entries="@array/quality_download"
            app:key="qualityDownload"
            app:title="@string/pic_quality_download"
            app:useSimpleSummaryProvider="true" />
        <ListPreference
            android:entries="@array/language"
            android:entryValues="@array/language_values"
            app:defaultValue="-1"
            app:key="language"
            app:title="Language" />
    </PreferenceCategory>

    <PreferenceCategory app:title="DEV">
        <SwitchPreferenceCompat
            android:key="crashreport"
            app:defaultValue="true"
            app:title="@string/crash_report" />
        <Preference
            app:key="view_report"
            android:title="@string/view_report" />
        <Preference
            app:key="view_logs"
            android:title="Logs" />
        <Preference
            app:key="backup"
            android:title="Backup" />
    </PreferenceCategory>

</androidx.preference.PreferenceScreen>
