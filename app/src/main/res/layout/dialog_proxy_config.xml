<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 启用代理开关 -->
        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/proxy_enabled"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/proxy_enabled"
            android:textSize="16sp" />

        <!-- 代理服务器设置 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/proxy_settings_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/proxy_server_settings"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <!-- 代理主机 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="@string/proxy_host"
                    app:boxStrokeColor="?attr/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/proxy_host"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri|textNoSuggestions"
                        android:singleLine="true" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 代理端口 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="@string/proxy_port"
                    app:boxStrokeColor="?attr/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/proxy_port"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:singleLine="true"
                        android:text="8080" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 认证设置 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/auth_settings_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 需要认证开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/proxy_requires_auth"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/proxy_requires_auth"
                    android:textSize="14sp" />

                <!-- 用户名 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/username_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="@string/proxy_username"
                    app:boxStrokeColor="?attr/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/proxy_username"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:singleLine="true" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/password_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/proxy_password"
                    app:boxStrokeColor="?attr/colorPrimary"
                    app:endIconMode="password_toggle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/proxy_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:singleLine="true" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 测试连接按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/test_proxy_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@string/test_proxy_connection"
            app:icon="@android:drawable/ic_menu_search" />

        <!-- 帮助文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/proxy_help_text"
            android:textColor="?android:textColorSecondary"
            android:textSize="12sp" />

    </LinearLayout>
</ScrollView>
