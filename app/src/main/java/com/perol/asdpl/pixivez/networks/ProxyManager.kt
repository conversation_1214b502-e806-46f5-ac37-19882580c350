/*
 * MIT License
 *
 * Copyright (c) 2020 ultranity
 * Copyright (c) 2019 Perol_Notsfsssf
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE
 */

package com.perol.asdpl.pixivez.networks

import com.perol.asdpl.pixivez.data.ProxyConfig
import com.perol.asdpl.pixivez.services.PxEZApp
import okhttp3.Authenticator
import okhttp3.Credentials
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.net.Proxy

/**
 * HTTP代理管理器
 */
object ProxyManager {
    
    /**
     * 获取当前代理配置
     */
    fun getCurrentProxyConfig(): ProxyConfig {
        val pre = PxEZApp.instance.pre
        return ProxyConfig.fromPreferences(
            enabled = pre.getBoolean("proxy_enabled", false),
            host = pre.getString("proxy_host", "") ?: "",
            port = pre.getInt("proxy_port", 8080),
            requiresAuth = pre.getBoolean("proxy_requires_auth", false),
            username = pre.getString("proxy_username", "") ?: "",
            password = pre.getString("proxy_password", "") ?: ""
        )
    }

    /**
     * 保存代理配置到SharedPreferences
     */
    fun saveProxyConfig(config: ProxyConfig) {
        val pre = PxEZApp.instance.pre
        pre.edit().apply {
            putBoolean("proxy_enabled", config.enabled)
            putString("proxy_host", config.host)
            putInt("proxy_port", config.port)
            putBoolean("proxy_requires_auth", config.requiresAuth)
            putString("proxy_username", config.username)
            putString("proxy_password", config.password)
            apply()
        }
    }

    /**
     * 为OkHttpClient.Builder配置代理
     */
    fun configureProxy(builder: OkHttpClient.Builder): OkHttpClient.Builder {
        val config = getCurrentProxyConfig()
        
        if (config.enabled && config.isValid()) {
            val proxy = config.toProxy()
            if (proxy != null) {
                builder.proxy(proxy)
                
                // 如果需要认证，添加代理认证器
                if (config.requiresAuth) {
                    builder.proxyAuthenticator(object : Authenticator {
                        override fun authenticate(route: Route?, response: Response): Request? {
                            val credential = Credentials.basic(config.username, config.password)
                            return response.request.newBuilder()
                                .header("Proxy-Authorization", credential)
                                .build()
                        }
                    })
                }
            }
        }
        
        return builder
    }

    /**
     * 测试代理连接
     */
    fun testProxyConnection(config: ProxyConfig, callback: (Boolean, String?) -> Unit) {
        if (!config.isValid()) {
            callback(false, "代理配置无效")
            return
        }

        // 在后台线程测试连接
        Thread {
            try {
                val testClient = OkHttpClient.Builder().apply {
                    val proxy = config.toProxy()
                    if (proxy != null) {
                        proxy(proxy)
                        
                        if (config.requiresAuth) {
                            proxyAuthenticator(object : Authenticator {
                                override fun authenticate(route: Route?, response: Response): Request? {
                                    val credential = Credentials.basic(config.username, config.password)
                                    return response.request.newBuilder()
                                        .header("Proxy-Authorization", credential)
                                        .build()
                                }
                            })
                        }
                    }
                    connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                }.build()

                val request = Request.Builder()
                    .url("https://www.google.com")
                    .build()

                val response = testClient.newCall(request).execute()
                val success = response.isSuccessful
                response.close()
                
                callback(success, if (success) "代理连接成功" else "代理连接失败: ${response.code}")
            } catch (e: Exception) {
                callback(false, "代理连接失败: ${e.message}")
            }
        }.start()
    }
}
