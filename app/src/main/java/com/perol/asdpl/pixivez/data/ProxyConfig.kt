/*
 * MIT License
 *
 * Copyright (c) 2020 ultranity
 * Copyright (c) 2019 Perol_Notsfsssf
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE
 */

package com.perol.asdpl.pixivez.data

import java.net.InetSocketAddress
import java.net.Proxy

/**
 * HTTP代理配置数据类
 */
data class ProxyConfig(
    val enabled: Boolean = false,
    val host: String = "",
    val port: Int = 8080,
    val requiresAuth: Boolean = false,
    val username: String = "",
    val password: String = ""
) {
    /**
     * 验证代理配置是否有效
     */
    fun isValid(): Boolean {
        if (!enabled) return true
        if (host.isBlank() || port <= 0 || port > 65535) return false
        if (requiresAuth && (username.isBlank() || password.isBlank())) return false
        return true
    }

    /**
     * 创建Java Proxy对象
     */
    fun toProxy(): Proxy? {
        return if (enabled && isValid()) {
            Proxy(Proxy.Type.HTTP, InetSocketAddress(host, port))
        } else {
            null
        }
    }

    companion object {
        /**
         * 从SharedPreferences创建ProxyConfig
         */
        fun fromPreferences(
            enabled: Boolean,
            host: String,
            port: Int,
            requiresAuth: Boolean,
            username: String,
            password: String
        ): ProxyConfig {
            return ProxyConfig(
                enabled = enabled,
                host = host,
                port = port,
                requiresAuth = requiresAuth,
                username = username,
                password = password
            )
        }
    }
}
